import { z } from "zod";

export const registerSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Invalid email address"),
  password: z.string().min(6, "Password must be at least 6 characters"),
});

export const loginSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(1, "Password is required"),
});

export const updateProfileSchema = z.object({
  name: z.string().trim().min(1, "Name must be at least 1 character").optional(),
  email: z.string().trim().email("Invalid email address").optional(),
  profileImage: z.string().trim().optional(),
});

export const changePasswordSchema = z.object({
  oldPassword: z.string().min(1, "Current password is required"),
  newPassword: z.string().min(6, "New password must be at least 6 characters"),
  confirmPassword: z.string().min(1, "Password confirmation is required"),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "New password and confirmation password do not match",
  path: ["confirmPassword"],
}).refine((data) => data.newPassword !== data.oldPassword, {
  message: "New password must be different from current password",
  path: ["newPassword"],
});

export const createAdminSchema = z.object({
  name: z.string().trim().min(1, "Name is required"),
  email: z.string().trim().email("Invalid email address"),
  password: z.string().min(6, "Password must be at least 6 characters"),
});

export const updateUserStatusSchema = z.object({
  status: z.enum(["activated", "inactivated", "suspended", "banned"], {
    message: "Status must be one of: activated, inactivated, suspended, banned"
  }),
});