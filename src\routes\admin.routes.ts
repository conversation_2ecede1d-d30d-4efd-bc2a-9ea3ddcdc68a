import { createAdmin, getAllUsers, updateUserStatus, deleteUser } from "../controllers/admin.controller";
import { authorize, authorizeAdmin } from "../middleware/authorization";
import express, { RequestHandler } from "express";

const adminRoutes = express.Router();

// All admin routes require both authentication and admin privileges
adminRoutes.post("/create", authorize, authorizeAdmin, createAdmin as RequestHandler);
adminRoutes.get("/users", authorize, authorizeAdmin, getAllUsers as RequestHandler);
adminRoutes.put("/users/status/:userId", authorize, authorizeAdmin, updateUserStatus as RequestHandler);
adminRoutes.delete("/users/delete/:userId", authorize, authorizeAdmin, deleteUser as RequestHandler);

export default adminRoutes;
