
import mongoose, { Schema } from "mongoose";
import { Role, Status, User } from "../interfaces/user";




const userSchema: Schema<User> = new Schema({
  name: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  profileImage: { type: String, default: "" },
  role: { type: String, enum: Object.values(Role), default: "user" },
  status: { type: String, enum: Object.values(Status), default: "inactivated" },
},
  { versionKey: false,
    timestamps: true }
);

const userModel = mongoose.model<User>("User", userSchema);

export default userModel;
