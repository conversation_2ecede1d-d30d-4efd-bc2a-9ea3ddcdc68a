import { z } from "zod";

export const createAdminSchema = z.object({
  name: z.string().trim().min(1, "Name is required"),
  email: z.string().trim().email("Invalid email address"),
  password: z.string().min(6, "Password must be at least 6 characters"),
});

export const updateUserStatusSchema = z.object({
  status: z.enum(["activated", "inactivated", "suspended", "banned"], {
    message: "Status must be one of: activated, inactivated, suspended, banned"
  }),
});

export const getUsersQuerySchema = z.object({
  page: z.string().optional().transform((val) => Math.max(1, parseInt(val || '1') || 1)),
  limit: z.string().optional().transform((val) => Math.min(100, Math.max(1, parseInt(val || '10') || 10))),
  search: z.string().trim().optional(),
  sortBy: z.enum(["name", "email", "createdAt", "status"]).optional().default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).optional().default("desc"),
  status: z.enum(["activated", "inactivated", "suspended", "banned"]).optional(),
});
