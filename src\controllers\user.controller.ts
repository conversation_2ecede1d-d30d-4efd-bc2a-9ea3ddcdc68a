import bcrypt from 'bcrypt';
import { NextFunction, Request, Response } from "express";
import userModel from './../models/User';
import { registerSchema } from "../schemaValidation/user";
import sendOurEmail from "../utils/mailSender";
import { Status } from '../interfaces/user';

export const register = async (req: Request, res: Response, next: NextFunction) => {
  try {
    console.log("[REGISTER] Incoming request body:", req.body);
    // Validate request body
    const result = registerSchema.safeParse(req.body);
    if (!result.success) {
      console.log("[REGISTER] Validation failed:", result.error.issues);
      return res.status(400).json({ error: result.error.issues.map((e) => e.message) });
    }
    const { name, email, password } = result.data;
    // Check if user already exists
    const existingUser = await userModel.findOne({ email });
    if (existingUser) {
      console.log("[REGISTER] User already exists:", email);
      return res.status(400).json({ error: "User already exists" });
    }
    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);
    // Create user
    await userModel.create({
      name,
      email,
      password: hashedPassword,
    });
    console.log("[REGISTER] User created:", email);
    // Send verification email
    await sendOurEmail(email, "Verify your email", true);
    console.log("[REGISTER] Verification email sent to:", email);
    // Respond with generic message
    return res.status(201).json({
      message: "User registered successfully. An email was sent to your email so you can verify it."
    });
  } catch (error: any) {
    console.error("[REGISTER] Error:", error);
    next({ statusCode: 500, message: error.message || "Internal Server Error" });
  }
};

export const verifyEmail = async (req: Request, res: Response, next: NextFunction) => {
  try {
    console.log("[VERIFY EMAIL] Incoming request body:", req.body);
    const { email } = req.body;

    // Check if email is provided
    if (!email) {
      console.log("[VERIFY EMAIL] Email is required");
      return res.status(400).json({ error: "Email is required" });
    }

    // Check if user exists
    const user = await userModel.findOne({ email });
    if (!user) {
      console.log("[VERIFY EMAIL] User not found:", email);
      return res.status(404).json({ error: "User not found" });
    }

    // Check if user is already verified
    if (user.status === "activated") {
      console.log("[VERIFY EMAIL] Email already verified:", email);
      return res.status(400).json({ error: "Email is already verified" });
    }

    // Update user status to activated
    await userModel.updateOne({ email }, { status: Status.Active });
    console.log("[VERIFY EMAIL] User status updated to activated:", email);

    return res.status(200).json({
      message: "Email verified successfully! Your account is now active.",
      user: {
        name: user.name,
        email: user.email,
        status: "activated"
      }
    });
  } catch (error: any) {
    console.error("[VERIFY EMAIL] Error:", error);
    next({ statusCode: 500, message: error.message || "Internal Server Error" });
  }
};
