import bcrypt from 'bcrypt';
import { NextFunction, Request, Response } from "express";
import userModel from './../models/User';
import { registerSchema, loginSchema, updateProfileSchema, changePasswordSchema } from "../schemaValidation/user";
import sendOurEmail from "../utils/mailSender";
import { Status } from '../interfaces/user';
import { generateEmailVerificationToken, verifyEmailVerificationToken, generateAccessToken, generateRefreshToken, verifyRefreshToken, getTokenExpiryInMs } from '../utils/generateToken';

export const register = async (req: Request, res: Response, next: NextFunction) => {
  try {
    console.log("[REGISTER] Incoming request body:", req.body);
    // Validate request body
    const result = registerSchema.safeParse(req.body);
    if (!result.success) {
      console.log("[REGISTER] Validation failed:", result.error.issues);
      return res.status(400).json({ error: result.error.issues.map((e) => e.message) });
    }
    const { name, email, password } = result.data;
    // Check if user already exists
    const existingUser = await userModel.findOne({ email });
    if (existingUser) {
      console.log("[REGISTER] User already exists:", email);
      return res.status(400).json({ error: "User already exists" });
    }
    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);
    // Create user
    const newUser = await userModel.create({
      name,
      email,
      password: hashedPassword,
    });
    console.log("[REGISTER] User created:", email);

    // Generate email verification token
    const verificationToken = generateEmailVerificationToken(newUser._id.toString(), email);
    console.log("[REGISTER] Verification token generated for:", email);

    // Send verification email with token
    await sendOurEmail(email, "Verify your email", true, verificationToken);
    console.log("[REGISTER] Verification email sent to:", email);
    // Respond with generic message
    return res.status(201).json({
      message: "User registered successfully. An email was sent to your email so you can verify it."
    });
  } catch (error: any) {
    console.error("[REGISTER] Error:", error);
    next({ statusCode: 500, message: error.message || "Internal Server Error" });
  }
};

export const verifyEmail = async (req: Request, res: Response, next: NextFunction) => {
  try {
    console.log("[VERIFY EMAIL] Incoming request params:", req.params);
    const { token } = req.params;

    // Check if token is provided
    if (!token) {
      console.log("[VERIFY EMAIL] Verification token is required");
      return res.status(400).json({ error: "Verification token is required" });
    }

    // Verify and decode the JWT token
    let decodedToken;
    try {
      decodedToken = verifyEmailVerificationToken(token);
      console.log("[VERIFY EMAIL] Token verified for user:", decodedToken.userId);
    } catch (error: any) {
      console.log("[VERIFY EMAIL] Token verification failed:", error.message);
      return res.status(400).json({ error: error.message });
    }

    // Find user by ID from token
    const user = await userModel.findById(decodedToken.userId);
    if (!user) {
      console.log("[VERIFY EMAIL] User not found:", decodedToken.userId);
      return res.status(404).json({ error: "User not found" });
    }

    // Verify email matches token
    if (user.email !== decodedToken.email) {
      console.log("[VERIFY EMAIL] Email mismatch for user:", decodedToken.userId);
      return res.status(400).json({ error: "Invalid verification token" });
    }

    // Check if user is already verified
    if (user.status === Status.Active) {
      console.log("[VERIFY EMAIL] Email already verified:", user.email);
      return res.status(400).json({ error: "Email is already verified" });
    }

    // Check if user is banned or suspended
    if (user.status === Status.Suspended) {
      console.log("[VERIFY EMAIL] User is suspended:", user.email);
      return res.status(403).json({ error: "Account is suspended. Please contact support." });
    }

    if (user.status === Status.Banned) {
      console.log("[VERIFY EMAIL] User is banned:", user.email);
      return res.status(403).json({ error: "Account is banned. Please contact support." });
    }

    // Update user status to activated
    await userModel.updateOne({ _id: decodedToken.userId }, { status: Status.Active });
    console.log("[VERIFY EMAIL] User status updated to activated:", user.email);

    return res.status(200).json({
      message: "Email verified successfully! Your account is now active.",
      user: {
        name: user.name,
        email: user.email,
        status: "activated"
      }
    });
  } catch (error: any) {
    console.error("[VERIFY EMAIL] Error:", error);
    next({ statusCode: 500, message: error.message || "Internal Server Error" });
  }
};

export const signIn = async (req: Request, res: Response, next: NextFunction) => {
  try {
    console.log("[SIGN IN] Incoming request body:", req.body);

    // Validate request body
    const result = loginSchema.safeParse(req.body);
    if (!result.success) {
      console.log("[SIGN IN] Validation failed:", result.error.issues);
      return res.status(400).json({ error: result.error.issues.map((e) => e.message) });
    }

    const { email, password } = result.data;

    // Check if user exists
    const user = await userModel.findOne({ email });
    if (!user) {
      console.log("[SIGN IN] User not found:", email);
      return res.status(401).json({ error: "Invalid email or password" });
    }

    // Check account status
    if (user.status === Status.Inactive) {
      console.log("[SIGN IN] Account not activated:", email);
      return res.status(403).json({ error: "Account not activated. Please verify your email first." });
    }

    if (user.status === Status.Suspended) {
      console.log("[SIGN IN] Account suspended:", email);
      return res.status(403).json({ error: "Account is suspended. Please contact support." });
    }

    if (user.status === Status.Banned) {
      console.log("[SIGN IN] Account banned:", email);
      return res.status(403).json({ error: "Account is banned. Please contact support." });
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      console.log("[SIGN IN] Invalid password for user:", email);
      return res.status(401).json({ error: "Invalid email or password" });
    }

    // Generate tokens
    const accessToken = generateAccessToken(user._id.toString());
    const refreshToken = generateRefreshToken(user._id.toString());

    console.log("[SIGN IN] Tokens generated for user:", email);

    // Set cookies
    const cookieOptions = {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict' as const,
    };

    // Get expiry times from JWT settings to keep cookie and token in sync
    const accessTokenExpiry = getTokenExpiryInMs(process.env.JWT_EXPIRATION_TIME || '1h');
    const refreshTokenExpiry = getTokenExpiryInMs(process.env.JWT_REFRESH_EXPIRATION_TIME || '7d');

    res.cookie('accessToken', accessToken, {
      ...cookieOptions,
      maxAge: accessTokenExpiry, // Same as JWT expiration
    });

    res.cookie('refreshToken', refreshToken, {
      ...cookieOptions,
      maxAge: refreshTokenExpiry, // Same as JWT expiration
    });

    console.log("[SIGN IN] User signed in successfully:", email);

    return res.status(200).json({
      message: "Sign in successful",
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        role: user.role,
        status: user.status,
        profileImage: user.profileImage
      }
    });

  } catch (error: any) {
    console.error("[SIGN IN] Error:", error);
    next({ statusCode: 500, message: error.message || "Internal Server Error" });
  }
};

export const refreshToken = async (req: Request, res: Response, next: NextFunction) => {
  try {
    console.log("[REFRESH TOKEN] Processing refresh token request");

    // Get refresh token from cookies
    const refreshTokenFromCookie = req.cookies.refreshToken;

    if (!refreshTokenFromCookie) {
      console.log("[REFRESH TOKEN] No refresh token found in cookies");
      return res.status(401).json({ error: "Refresh token not found" });
    }

    // Verify refresh token
    let decodedToken;
    try {
      decodedToken = verifyRefreshToken(refreshTokenFromCookie);
      console.log("[REFRESH TOKEN] Token verified for user:", decodedToken.userId);
    } catch (error: any) {
      console.log("[REFRESH TOKEN] Token verification failed:", error.message);
      return res.status(401).json({ error: error.message });
    }

    // Check if user still exists and is active
    const user = await userModel.findById(decodedToken.userId);
    if (!user) {
      console.log("[REFRESH TOKEN] User not found:", decodedToken.userId);
      return res.status(401).json({ error: "User not found" });
    }

    // Check account status
    if (user.status !== Status.Active) {
      console.log("[REFRESH TOKEN] User account not active:", user.email);
      return res.status(403).json({ error: "Account is not active" });
    }

    // Generate new access token
    const newAccessToken = generateAccessToken(user._id.toString());

    console.log("[REFRESH TOKEN] New access token generated for user:", user.email);

    // Set new access token cookie with same expiry as JWT
    const accessTokenExpiry = getTokenExpiryInMs(process.env.JWT_EXPIRATION_TIME || '1h');

    const cookieOptions = {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict' as const,
      maxAge: accessTokenExpiry, 
    };

    res.cookie('accessToken', newAccessToken, cookieOptions);

    return res.status(200).json({
      message: "Access token refreshed successfully"});

  } catch (error: any) {
    console.error("[REFRESH TOKEN] Error:", error);
    next({ statusCode: 500, message: error.message || "Internal Server Error" });
  }
};

export const signOut = async (req: Request, res: Response, next: NextFunction) => {
  try {
    console.log("[SIGN OUT] Processing sign out request");

    // Clear both access and refresh token cookies
    res.clearCookie('accessToken', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict'
    });

    res.clearCookie('refreshToken', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict'
    });

    console.log("[SIGN OUT] Cookies cleared successfully");

    return res.status(200).json({
      message: "Signed out successfully"
    });

  } catch (error: any) {
    console.error("[SIGN OUT] Error:", error);
    next({ statusCode: 500, message: error.message || "Internal Server Error" });
  }
};

export const updateProfile = async (req: Request, res: Response, next: NextFunction) => {
  try {
    console.log("[UPDATE PROFILE] Incoming request body:", req.body);
    console.log("[UPDATE PROFILE] User ID from token:", req.user?.userId);

    // Validate request body
    const result = updateProfileSchema.safeParse(req.body);
    if (!result.success) {
      console.log("[UPDATE PROFILE] Validation failed:", result.error.issues);
      return res.status(400).json({ error: result.error.issues.map((e) => e.message) });
    }

    const updateData = result.data;
    const userId = req.user?.userId;

    if (!userId) {
      console.log("[UPDATE PROFILE] No user ID found in token");
      return res.status(401).json({ error: "Authentication required" });
    }

    const updatedUser = await userModel.findByIdAndUpdate(
      userId,
      updateData,
      { new: true, runValidators: true }
    ).select('-password'); // Exclude password from response

    if (!updatedUser) {
      console.log("[UPDATE PROFILE] User not found:", userId);
      return res.status(404).json({ error: "User not found" });
    }

    console.log("[UPDATE PROFILE] Profile updated successfully for user:", updatedUser.email);

    return res.status(200).json({
      message: "Profile updated successfully",
      user: {
        name: updatedUser.name,
        email: updatedUser.email,
        role: updatedUser.role,
        status: updatedUser.status,
        profileImage: updatedUser.profileImage
      }
    });

  } catch (error: any) {
    console.error("[UPDATE PROFILE] Error:", error);

    // Handle MongoDB duplicate key error for email
    if (error.code === 11000 && error.keyPattern?.email) {
      console.log("[UPDATE PROFILE] Duplicate email error:", error.keyValue?.email);
      return res.status(409).json({ error: "Email already exists" });
    }

    next({ statusCode: 500, message: error.message || "Internal Server Error" });
  }
};

export const changePassword = async (req: Request, res: Response, next: NextFunction) => {
  try {
    console.log("[CHANGE PASSWORD] Processing password change request");
    console.log("[CHANGE PASSWORD] User ID from token:", req.user?.userId);

    // Validate request body
    const result = changePasswordSchema.safeParse(req.body);
    if (!result.success) {
      console.log("[CHANGE PASSWORD] Validation failed:", result.error.issues);
      return res.status(400).json({ error: result.error.issues.map((e) => e.message) });
    }

    const { oldPassword, newPassword } = result.data;
    const userId = req.user?.userId;

    if (!userId) {
      console.log("[CHANGE PASSWORD] No user ID found in token");
      return res.status(401).json({ error: "Authentication required" });
    }

    // Find user and include password for verification
    const user = await userModel.findById(userId);
    if (!user) {
      console.log("[CHANGE PASSWORD] User not found:", userId);
      return res.status(404).json({ error: "User not found" });
    }

    // Verify old password
    const isOldPasswordValid = await bcrypt.compare(oldPassword, user.password);
    if (!isOldPasswordValid) {
      console.log("[CHANGE PASSWORD] Invalid old password for user:", user.email);
      return res.status(401).json({ error: "Current password is incorrect" });
    }

    // Hash new password
    const hashedNewPassword = await bcrypt.hash(newPassword, 10);

    // Update password in database
    await userModel.findByIdAndUpdate(userId, { password: hashedNewPassword });

    console.log("[CHANGE PASSWORD] Password changed successfully for user:", user.email);

    // Clear cookies to force re-authentication for security
    res.clearCookie('accessToken', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict'
    });

    res.clearCookie('refreshToken', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict'
    });

    console.log("[CHANGE PASSWORD] Authentication cookies cleared for security");

    return res.status(200).json({
      message: "Password changed successfully. Please sign in again with your new password."
    });

  } catch (error: any) {
    console.error("[CHANGE PASSWORD] Error:", error);
    next({ statusCode: 500, message: error.message || "Internal Server Error" });
  }
};
