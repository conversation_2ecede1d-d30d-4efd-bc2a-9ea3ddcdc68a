import bcrypt from 'bcrypt';
import { NextFunction, Request, Response } from "express";
import userModel from './../models/User';
import { registerSchema, loginSchema, updateProfileSchema, changePasswordSchema } from "../schemaValidation/user";
import { createAdminSchema, updateUserStatusSchema, getUsersQuerySchema } from "../schemaValidation/admin";
import sendOurEmail from "../utils/mailSender";
import { Status, Role } from '../interfaces/user';
import { generateEmailVerificationToken, verifyEmailVerificationToken, generateAccessToken, generateRefreshToken, verifyRefreshToken, getTokenExpiryInMs } from '../utils/generateToken';

export const register = async (req: Request, res: Response, next: NextFunction) => {
  try {
    console.log("[REGISTER] Incoming request body:", req.body);
    // Validate request body
    const result = registerSchema.safeParse(req.body);
    if (!result.success) {
      console.log("[REGISTER] Validation failed:", result.error.issues);
      return res.status(400).json({ error: result.error.issues.map((e) => e.message) });
    }
    const { name, email, password } = result.data;
    // Check if user already exists
    const existingUser = await userModel.findOne({ email });
    if (existingUser) {
      console.log("[REGISTER] User already exists:", email);
      return res.status(400).json({ error: "User already exists" });
    }
    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);
    // Create user
    const newUser = await userModel.create({
      name,
      email,
      password: hashedPassword,
    });
    console.log("[REGISTER] User created:", email);

    // Generate email verification token
    const verificationToken = generateEmailVerificationToken(newUser._id.toString(), email);
    console.log("[REGISTER] Verification token generated for:", email);

    // Send verification email with token
    await sendOurEmail(email, "Verify your email", true, verificationToken);
    console.log("[REGISTER] Verification email sent to:", email);
    // Respond with generic message
    return res.status(201).json({
      message: "User registered successfully. An email was sent to your email so you can verify it."
    });
  } catch (error: any) {
    console.error("[REGISTER] Error:", error);
    next({ statusCode: 500, message: error.message || "Internal Server Error" });
  }
};

export const verifyEmail = async (req: Request, res: Response, next: NextFunction) => {
  try {
    console.log("[VERIFY EMAIL] Incoming request params:", req.params);
    const { token } = req.params;

    // Check if token is provided
    if (!token) {
      console.log("[VERIFY EMAIL] Verification token is required");
      return res.status(400).json({ error: "Verification token is required" });
    }

    // Verify and decode the JWT token
    let decodedToken;
    try {
      decodedToken = verifyEmailVerificationToken(token);
      console.log("[VERIFY EMAIL] Token verified for user:", decodedToken.userId);
    } catch (error: any) {
      console.log("[VERIFY EMAIL] Token verification failed:", error.message);
      return res.status(400).json({ error: error.message });
    }

    // Find user by ID from token
    const user = await userModel.findById(decodedToken.userId);
    if (!user) {
      console.log("[VERIFY EMAIL] User not found:", decodedToken.userId);
      return res.status(404).json({ error: "User not found" });
    }

    // Verify email matches token
    if (user.email !== decodedToken.email) {
      console.log("[VERIFY EMAIL] Email mismatch for user:", decodedToken.userId);
      return res.status(400).json({ error: "Invalid verification token" });
    }

    // Check if user is already verified
    if (user.status === Status.Active) {
      console.log("[VERIFY EMAIL] Email already verified:", user.email);
      return res.status(400).json({ error: "Email is already verified" });
    }

    // Check if user is banned or suspended
    if (user.status === Status.Suspended) {
      console.log("[VERIFY EMAIL] User is suspended:", user.email);
      return res.status(403).json({ error: "Account is suspended. Please contact support." });
    }

    if (user.status === Status.Banned) {
      console.log("[VERIFY EMAIL] User is banned:", user.email);
      return res.status(403).json({ error: "Account is banned. Please contact support." });
    }

    // Update user status to activated
    await userModel.updateOne({ _id: decodedToken.userId }, { status: Status.Active });
    console.log("[VERIFY EMAIL] User status updated to activated:", user.email);

    return res.status(200).json({
      message: "Email verified successfully! Your account is now active.",
      user: {
        name: user.name,
        email: user.email,
        status: "activated"
      }
    });
  } catch (error: any) {
    console.error("[VERIFY EMAIL] Error:", error);
    next({ statusCode: 500, message: error.message || "Internal Server Error" });
  }
};

export const signIn = async (req: Request, res: Response, next: NextFunction) => {
  try {
    console.log("[SIGN IN] Incoming request body:", req.body);

    // Validate request body
    const result = loginSchema.safeParse(req.body);
    if (!result.success) {
      console.log("[SIGN IN] Validation failed:", result.error.issues);
      return res.status(400).json({ error: result.error.issues.map((e) => e.message) });
    }

    const { email, password } = result.data;

    // Check if user exists
    const user = await userModel.findOne({ email });
    if (!user) {
      console.log("[SIGN IN] User not found:", email);
      return res.status(401).json({ error: "Invalid email or password" });
    }

    // Check account status
    if (user.status === Status.Inactive) {
      console.log("[SIGN IN] Account not activated:", email);
      return res.status(403).json({ error: "Account not activated. Please verify your email first." });
    }

    if (user.status === Status.Suspended) {
      console.log("[SIGN IN] Account suspended:", email);
      return res.status(403).json({ error: "Account is suspended. Please contact support." });
    }

    if (user.status === Status.Banned) {
      console.log("[SIGN IN] Account banned:", email);
      return res.status(403).json({ error: "Account is banned. Please contact support." });
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      console.log("[SIGN IN] Invalid password for user:", email);
      return res.status(401).json({ error: "Invalid email or password" });
    }

    // Generate tokens
    const accessToken = generateAccessToken(user._id.toString());
    const refreshToken = generateRefreshToken(user._id.toString());

    console.log("[SIGN IN] Tokens generated for user:", email);

    // Set cookies
    const cookieOptions = {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict' as const,
    };

    // Get expiry times from JWT settings to keep cookie and token in sync
    const accessTokenExpiry = getTokenExpiryInMs(process.env.JWT_EXPIRATION_TIME || '1h');
    const refreshTokenExpiry = getTokenExpiryInMs(process.env.JWT_REFRESH_EXPIRATION_TIME || '7d');

    res.cookie('accessToken', accessToken, {
      ...cookieOptions,
      maxAge: accessTokenExpiry, // Same as JWT expiration
    });

    res.cookie('refreshToken', refreshToken, {
      ...cookieOptions,
      maxAge: refreshTokenExpiry, // Same as JWT expiration
    });

    console.log("[SIGN IN] User signed in successfully:", email);

    return res.status(200).json({
      message: "Sign in successful",
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        role: user.role,
        status: user.status,
        profileImage: user.profileImage
      }
    });

  } catch (error: any) {
    console.error("[SIGN IN] Error:", error);
    next({ statusCode: 500, message: error.message || "Internal Server Error" });
  }
};

export const refreshToken = async (req: Request, res: Response, next: NextFunction) => {
  try {
    console.log("[REFRESH TOKEN] Processing refresh token request");

    // Get refresh token from cookies
    const refreshTokenFromCookie = req.cookies.refreshToken;

    if (!refreshTokenFromCookie) {
      console.log("[REFRESH TOKEN] No refresh token found in cookies");
      return res.status(401).json({ error: "Refresh token not found" });
    }

    // Verify refresh token
    let decodedToken;
    try {
      decodedToken = verifyRefreshToken(refreshTokenFromCookie);
      console.log("[REFRESH TOKEN] Token verified for user:", decodedToken.userId);
    } catch (error: any) {
      console.log("[REFRESH TOKEN] Token verification failed:", error.message);
      return res.status(401).json({ error: error.message });
    }

    // Check if user still exists and is active
    const user = await userModel.findById(decodedToken.userId);
    if (!user) {
      console.log("[REFRESH TOKEN] User not found:", decodedToken.userId);
      return res.status(401).json({ error: "User not found" });
    }

    // Check account status
    if (user.status !== Status.Active) {
      console.log("[REFRESH TOKEN] User account not active:", user.email);
      return res.status(403).json({ error: "Account is not active" });
    }

    // Generate new access token
    const newAccessToken = generateAccessToken(user._id.toString());

    console.log("[REFRESH TOKEN] New access token generated for user:", user.email);

    // Set new access token cookie with same expiry as JWT
    const accessTokenExpiry = getTokenExpiryInMs(process.env.JWT_EXPIRATION_TIME || '1h');

    const cookieOptions = {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict' as const,
      maxAge: accessTokenExpiry, 
    };

    res.cookie('accessToken', newAccessToken, cookieOptions);

    return res.status(200).json({
      message: "Access token refreshed successfully"});

  } catch (error: any) {
    console.error("[REFRESH TOKEN] Error:", error);
    next({ statusCode: 500, message: error.message || "Internal Server Error" });
  }
};

export const signOut = async (req: Request, res: Response, next: NextFunction) => {
  try {
    console.log("[SIGN OUT] Processing sign out request");

    // Clear both access and refresh token cookies
    res.clearCookie('accessToken', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict'
    });

    res.clearCookie('refreshToken', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict'
    });

    console.log("[SIGN OUT] Cookies cleared successfully");

    return res.status(200).json({
      message: "Signed out successfully"
    });

  } catch (error: any) {
    console.error("[SIGN OUT] Error:", error);
    next({ statusCode: 500, message: error.message || "Internal Server Error" });
  }
};

export const updateProfile = async (req: Request, res: Response, next: NextFunction) => {
  try {
    console.log("[UPDATE PROFILE] Incoming request body:", req.body);
    console.log("[UPDATE PROFILE] User ID from token:", req.user?.userId);

    // Validate request body
    const result = updateProfileSchema.safeParse(req.body);
    if (!result.success) {
      console.log("[UPDATE PROFILE] Validation failed:", result.error.issues);
      return res.status(400).json({ error: result.error.issues.map((e) => e.message) });
    }

    const updateData = result.data;
    const userId = req.user?.userId;

    if (!userId) {
      console.log("[UPDATE PROFILE] No user ID found in token");
      return res.status(401).json({ error: "Authentication required" });
    }

    const updatedUser = await userModel.findByIdAndUpdate(
      userId,
      updateData,
      { new: true, runValidators: true }
    ).select('-password'); // Exclude password from response

    if (!updatedUser) {
      console.log("[UPDATE PROFILE] User not found:", userId);
      return res.status(404).json({ error: "User not found" });
    }

    console.log("[UPDATE PROFILE] Profile updated successfully for user:", updatedUser.email);

    return res.status(200).json({
      message: "Profile updated successfully",
      user: {
        name: updatedUser.name,
        email: updatedUser.email,
        role: updatedUser.role,
        status: updatedUser.status,
        profileImage: updatedUser.profileImage
      }
    });

  } catch (error: any) {
    console.error("[UPDATE PROFILE] Error:", error);

    // Handle MongoDB duplicate key error for email
    if (error.code === 11000 && error.keyPattern?.email) {
      console.log("[UPDATE PROFILE] Duplicate email error:", error.keyValue?.email);
      return res.status(409).json({ error: "Email already exists" });
    }

    next({ statusCode: 500, message: error.message || "Internal Server Error" });
  }
};

export const changePassword = async (req: Request, res: Response, next: NextFunction) => {
  try {
    console.log("[CHANGE PASSWORD] Processing password change request");
    console.log("[CHANGE PASSWORD] User ID from token:", req.user?.userId);

    // Validate request body
    const result = changePasswordSchema.safeParse(req.body);
    if (!result.success) {
      console.log("[CHANGE PASSWORD] Validation failed:", result.error.issues);
      return res.status(400).json({ error: result.error.issues.map((e) => e.message) });
    }

    const { oldPassword, newPassword } = result.data;
    const userId = req.user?.userId;

    if (!userId) {
      console.log("[CHANGE PASSWORD] No user ID found in token");
      return res.status(401).json({ error: "Authentication required" });
    }

    // Find user and include password for verification
    const user = await userModel.findById(userId);
    if (!user) {
      console.log("[CHANGE PASSWORD] User not found:", userId);
      return res.status(404).json({ error: "User not found" });
    }

    // Verify old password
    const isOldPasswordValid = await bcrypt.compare(oldPassword, user.password);
    if (!isOldPasswordValid) {
      console.log("[CHANGE PASSWORD] Invalid old password for user:", user.email);
      return res.status(401).json({ error: "Current password is incorrect" });
    }

    // Hash new password
    const hashedNewPassword = await bcrypt.hash(newPassword, 10);

    // Update password in database
    await userModel.findByIdAndUpdate(userId, { password: hashedNewPassword });

    console.log("[CHANGE PASSWORD] Password changed successfully for user:", user.email);

    // Clear cookies to force re-authentication for security
    res.clearCookie('accessToken', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict'
    });

    res.clearCookie('refreshToken', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict'
    });

    console.log("[CHANGE PASSWORD] Authentication cookies cleared for security");

    return res.status(200).json({
      message: "Password changed successfully. Please sign in again with your new password."
    });

  } catch (error: any) {
    console.error("[CHANGE PASSWORD] Error:", error);
    next({ statusCode: 500, message: error.message || "Internal Server Error" });
  }
};

// ==================== ADMIN ENDPOINTS ====================

export const createAdmin = async (req: Request, res: Response, next: NextFunction) => {
  try {
    console.log("[CREATE ADMIN] Incoming request body:", req.body);
    console.log("[CREATE ADMIN] Requesting admin ID:", req.user?.userId);

    // Validate request body
    const result = createAdminSchema.safeParse(req.body);
    if (!result.success) {
      console.log("[CREATE ADMIN] Validation failed:", result.error.issues);
      return res.status(400).json({ error: result.error.issues.map((e) => e.message) });
    }

    const { name, email, password } = result.data;

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create admin user
    const newAdmin = await userModel.create({
      name,
      email,
      password: hashedPassword,
      role: Role.Admin,
      status: Status.Active, // Admins are activated by default
    });

    console.log("[CREATE ADMIN] Admin created successfully:", email);

    return res.status(201).json({
      message: "Admin created successfully",
      admin: {
        id: newAdmin._id,
        name: newAdmin.name,
        email: newAdmin.email,
        role: newAdmin.role,
        status: newAdmin.status,
        profileImage: newAdmin.profileImage
      }
    });

  } catch (error: any) {
    console.error("[CREATE ADMIN] Error:", error);

    // Handle MongoDB duplicate key error for email
    if (error.code === 11000 && error.keyPattern?.email) {
      console.log("[CREATE ADMIN] Duplicate email error:", error.keyValue?.email);
      return res.status(409).json({ error: "Email already exists" });
    }

    next({ statusCode: 500, message: error.message || "Internal Server Error" });
  }
};

export const getAllUsers = async (req: Request, res: Response, next: NextFunction) => {
  try {
    console.log("[GET ALL USERS] Query parameters:", req.query);
    console.log("[GET ALL USERS] Requesting admin ID:", req.user?.userId);

    // Validate and parse query parameters
    const queryResult = getUsersQuerySchema.safeParse(req.query);
    if (!queryResult.success) {
      console.log("[GET ALL USERS] Query validation failed:", queryResult.error.issues);
      return res.status(400).json({ error: queryResult.error.issues.map((e) => e.message) });
    }

    const { page, limit, search, sortBy, sortOrder, status: statusFilter } = queryResult.data;

    // Build search query
    const searchQuery: any = {};

    // Add search by name or email
    if (search) {
      searchQuery.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ];
    }

    // Add status filter
    if (statusFilter && ['activated', 'inactivated', 'suspended', 'banned'].includes(statusFilter)) {
      searchQuery.status = statusFilter;
    }

    // Build sort object
    const sortOptions: any = {};
    const sortDirection = sortOrder === 'desc' ? -1 : 1;
    sortOptions[sortBy] = sortDirection;

    // Calculate skip value for pagination
    const skip = (page - 1) * limit;

    // Get total count for pagination metadata
    const totalUsers = await userModel.countDocuments(searchQuery);

    // Get users with pagination, search, and sorting
    const users = await userModel
      .find(searchQuery)
      .select('-password') // Exclude password field
      .sort(sortOptions)
      .skip(skip)
      .limit(limit)
      .lean(); // Use lean() for better performance

    // Calculate pagination metadata
    const totalPages = Math.ceil(totalUsers / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    console.log(`[GET ALL USERS] Retrieved ${users.length} users (page ${page}/${totalPages})`);

    return res.status(200).json({
      message: "Users retrieved successfully",
      users: users.map(user => ({
        id: user._id,
        name: user.name,
        email: user.email,
        role: user.role,
        status: user.status,
        profileImage: user.profileImage
      })),
      pagination: {
        totalUsers,
        totalPages,
        currentPage: page,
        limit,
        hasNextPage,
        hasPrevPage
      },
      filters: {
        search: search || null,
        status: statusFilter || null,
        sortBy,
        sortOrder
      }
    });

  } catch (error: any) {
    console.error("[GET ALL USERS] Error:", error);
    next({ statusCode: 500, message: error.message || "Internal Server Error" });
  }
};

export const updateUserStatus = async (req: Request, res: Response, next: NextFunction) => {
  try {
    console.log("[UPDATE USER STATUS] Incoming request body:", req.body);
    console.log("[UPDATE USER STATUS] Target user ID:", req.params.userId);
    console.log("[UPDATE USER STATUS] Requesting admin ID:", req.user?.userId);

    // Validate request body
    const result = updateUserStatusSchema.safeParse(req.body);
    if (!result.success) {
      console.log("[UPDATE USER STATUS] Validation failed:", result.error.issues);
      return res.status(400).json({ error: result.error.issues.map((e) => e.message) });
    }

    const { status } = result.data;
    const targetUserId = req.params.userId;
    const adminUserId = req.user?.userId;

    if (!adminUserId) {
      console.log("[UPDATE USER STATUS] No admin ID found in token");
      return res.status(401).json({ error: "Authentication required" });
    }

    // Prevent admin from changing their own status
    if (targetUserId === adminUserId) {
      console.log("[UPDATE USER STATUS] Admin attempting to change own status");
      return res.status(403).json({ error: "Cannot change your own account status" });
    }

    // Find the target user
    const targetUser = await userModel.findById(targetUserId);
    if (!targetUser) {
      console.log("[UPDATE USER STATUS] Target user not found:", targetUserId);
      return res.status(404).json({ error: "User not found" });
    }

    // Prevent changing status of other admins
    if (targetUser.role === Role.Admin) {
      console.log("[UPDATE USER STATUS] Attempting to change admin status:", targetUser.email);
      return res.status(403).json({ error: "Cannot change status of admin accounts" });
    }

    // Update user status
    const updatedUser = await userModel.findByIdAndUpdate(
      targetUserId,
      { status },
      { new: true, runValidators: true }
    ).select('-password');

    if (!updatedUser) {
      console.log("[UPDATE USER STATUS] Failed to update user:", targetUserId);
      return res.status(404).json({ error: "User not found" });
    }

    console.log(`[UPDATE USER STATUS] User status updated: ${updatedUser.email} -> ${status}`);

    return res.status(200).json({
      message: `User status updated to ${status} successfully`,
      user: {
        id: updatedUser._id,
        name: updatedUser.name,
        email: updatedUser.email,
        role: updatedUser.role,
        status: updatedUser.status,
        profileImage: updatedUser.profileImage
      }
    });

  } catch (error: any) {
    console.error("[UPDATE USER STATUS] Error:", error);
    next({ statusCode: 500, message: error.message || "Internal Server Error" });
  }
};

export const deleteUser = async (req: Request, res: Response, next: NextFunction) => {
  try {
    console.log("[DELETE USER] Target user ID:", req.params.userId);
    console.log("[DELETE USER] Requesting admin ID:", req.user?.userId);

    const targetUserId = req.params.userId;
    const adminUserId = req.user?.userId;

    if (!adminUserId) {
      console.log("[DELETE USER] No admin ID found in token");
      return res.status(401).json({ error: "Authentication required" });
    }

    // Prevent admin from deleting themselves
    if (targetUserId === adminUserId) {
      console.log("[DELETE USER] Admin attempting to delete own account");
      return res.status(403).json({ error: "Cannot delete your own account" });
    }

    // Find the target user first to check if they exist and get their role
    const targetUser = await userModel.findById(targetUserId);
    if (!targetUser) {
      console.log("[DELETE USER] Target user not found:", targetUserId);
      return res.status(404).json({ error: "User not found" });
    }

    // Prevent deleting other admin accounts
    if (targetUser.role === Role.Admin) {
      console.log("[DELETE USER] Attempting to delete admin account:", targetUser.email);
      return res.status(403).json({ error: "Cannot delete admin accounts" });
    }

    // Delete the user
    const deletedUser = await userModel.findByIdAndDelete(targetUserId);

    if (!deletedUser) {
      console.log("[DELETE USER] Failed to delete user:", targetUserId);
      return res.status(404).json({ error: "User not found" });
    }

    console.log(`[DELETE USER] User deleted successfully: ${deletedUser.email}`);

    return res.status(200).json({
      message: `User ${deletedUser.email} has been permanently deleted`
    });

  } catch (error: any) {
    console.error("[DELETE USER] Error:", error);
    next({ statusCode: 500, message: error.message || "Internal Server Error" });
  }
};
