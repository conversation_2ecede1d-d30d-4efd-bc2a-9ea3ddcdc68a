import express, { Request, Response } from "express";
import cors from "cors";
import dotenv from "dotenv";
import { DBconnection } from "./config/db";
import { errorHandler } from "./middleware/errorHandler";
import { notFound } from "./middleware/notFound";
import userRoutes from "./routes/user.routes";

dotenv.config();

const app = express();
const PORT = process.env.PORT ?? 3000;
DBconnection();
app.use(cors());
app.use(express.json());
//
app.get("/test", (_, res: Response) => {
  res.send("Hello From Server");
});
//
app.use("/v0/auth", userRoutes);
app.use(notFound);
app.use(errorHandler);
app.listen(PORT, () => {
  console.log(`Server Running on Port ${PORT}`);
});
