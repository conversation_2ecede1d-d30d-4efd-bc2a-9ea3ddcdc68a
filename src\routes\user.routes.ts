
import { register, verifyEmail, signIn, refreshToken, signOut, updateProfile, changePassword, createAdmin, getAllUsers, updateUserStatus, deleteUser } from "../controllers/user.controller";
import { authorize, authorizeAdmin } from "../middleware/authorization";
import express, { RequestHandler } from "express";

const userRoutes = express.Router();



userRoutes.post("/signup", register as RequestHandler);
userRoutes.get("/verify-email/:token", verifyEmail as RequestHandler);
userRoutes.post("/signin", signIn as RequestHandler);
userRoutes.post("/refresh-token", refreshToken as RequestHandler);
userRoutes.post("/signout", signOut as RequestHandler);

// Protected routes - require authentication
userRoutes.put("/profile", authorize, updateProfile as RequestHandler);
userRoutes.put("/change-password", authorize, changePassword as <PERSON>questHandler);

// Admin routes - require admin authentication
userRoutes.post("/admin/create", authorize, authorizeAdmin, createAdmin as RequestHandler);
userRoutes.get("/admin/users", authorize, authorizeAdmin, getAllUsers as RequestHandler);
userRoutes.put("/admin/users/:userId/status", authorize, authorizeAdmin, updateUserStatus as RequestHandler);
userRoutes.delete("/admin/users/:userId", authorize, authorizeAdmin, deleteUser as RequestHandler);


export default userRoutes;