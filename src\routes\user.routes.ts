
import { register, verifyEmail, signIn, refreshToken, signOut } from "../controllers/user.controller";
import express, { RequestHandler } from "express";

const userRoutes = express.Router();



userRoutes.post("/signup", register as RequestHandler);
userRoutes.get("/verify-email/:token", verifyEmail as <PERSON>quest<PERSON>andler);
userRoutes.post("/signin", signIn as <PERSON>questHandler);
userRoutes.post("/refresh-token", refreshToken as RequestHandler);
userRoutes.post("/signout", signOut as RequestHandler);


export default userRoutes;