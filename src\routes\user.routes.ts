
import { register, verifyEmail, signIn, refreshToken, signOut, updateProfile, changePassword } from "../controllers/user.controller";
import { authorize } from "../middleware/authorization";
import express, { RequestHandler } from "express";

const userRoutes = express.Router();



userRoutes.post("/signup", register as <PERSON><PERSON><PERSON>andler);
userRoutes.get("/verify-email/:token", verifyEmail as RequestHandler);
userRoutes.post("/signin", signIn as RequestHandler);
userRoutes.post("/refresh-token", refreshToken as RequestHandler);
userRoutes.post("/signout", signOut as RequestHandler);

// Protected routes - require authentication
userRoutes.put("/profile", authorize, updateProfile as <PERSON>questHandler);
userRoutes.put("/change-password", authorize, changePassword as <PERSON>questHandler);


export default userRoutes;