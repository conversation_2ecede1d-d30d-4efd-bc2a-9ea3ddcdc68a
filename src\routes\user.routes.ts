
import { register, verifyEmail, signIn, refreshToken } from "../controllers/user.controller";
import express, { RequestHandler } from "express";

const userRoutes = express.Router();



userRoutes.post("/signup", register as RequestHandler);
userRoutes.get("/verify-email/:token", verifyEmail as <PERSON>quest<PERSON>and<PERSON>);
userRoutes.post("/signin", signIn as <PERSON>questHandler);
userRoutes.post("/refresh-token", refreshToken as RequestHandler);


export default userRoutes;